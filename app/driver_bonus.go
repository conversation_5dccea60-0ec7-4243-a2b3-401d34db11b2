package app

import (
	"context"
	"fmt"

	"billing_service/model"
)

type DriverBonusRequest struct {
	BonusAmount int `json:"bonus_amount"`
	ChallengeId int `json:"challenge_id,omitempty"`
}

func (a *App) ProcessDriverBonus(ctx context.Context, driverId int, req DriverBonusRequest) (errType string, err error) {
	if req.BonusAmount <= 0 {
		errType = "invalid_amount"
		err = fmt.Errorf("bonus amount must be greater than 0")
		return
	}
	// challange id is required
	var comment string

	if req.ChallengeId <= 0 {
		errType = "invalid_challenge_id"
		err = fmt.Errorf("challenge id is required")
		return
	}

	comment = fmt.Sprintf("Driver bonus payment for challenge %d", req.ChallengeId)

	a2cRequest := model.A2CPaymentRequest{
		OrderId:     0,
		DriverId:    driverId,
		Amount:      req.BonusAmount,
		Reason:      model.PaymentReasonDriverBonus,
		Comment:     comment,
		ChallengeId: req.ChallengeId,
	}

	// Queue the A2C payment task
	_, err = a.river.Insert(ctx, PayA2CArgs{A2CPaymentRequest: a2cRequest}, nil)
	if err != nil {
		err = fmt.Errorf("failed to queue bonus payment: %v", err)
		return
	}

	return
}
